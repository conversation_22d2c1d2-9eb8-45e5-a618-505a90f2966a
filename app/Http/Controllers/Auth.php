<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Auth as Authenticate;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class Auth extends Controller
{
    public function login(Request $request) {

        try {
            // Log CSRF token information for debugging
            Log::info('Login attempt', [
                'session_id' => $request->session()->getId(),
                'csrf_token' => $request->session()->token(),
                'has_csrf_token' => $request->has('_token'),
                'request_token' => $request->input('_token'),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
            $validator = Validator::make($request->all(), [
                'username' => 'required|max:50',
                'password' => 'required|min:6|max:50',
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => env('API_URL').'/login',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => array('username' => $request->username, 'password' => $request->password),
            ));

            $response = curl_exec($curl);
            $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($http_status == 200) {
                $result = json_decode($response);

                if ($result->result == true) {
                    // Store auth data in session
                    session([
                        'authToken' => $result->token,
                        'user' => $result->user,
                    ]);

                    // Regenerate session ID but keep session data
                    $request->session()->regenerate(false);

                    Log::info('Login successful', [
                        'username' => $request->username,
                        'ip' => $request->ip()
                    ]);

                    return redirect()->route('dashboard');
                } else {
                    // Handle specific password error
                    if (stripos($result->message, 'password') !== false) {
                        Log::warning('Login attempt with incorrect password', [
                            'username' => $request->username,
                            'ip' => $request->ip()
                        ]);
                        throw new Exception('Kata laluan tidak tepat');
                    } else {
                        throw new Exception($result->message);
                    }
                }
            } else {
                throw new Exception("Permintaan tidak berjaya. Sila cuba lagi.");
            }
        } catch (Exception $e) {
            Log::error('Login Error: ' . $e->getMessage(), [
                'username' => $request->username,
                'ip' => $request->ip()
            ]);
            return redirect()->back()->withErrors(['error' => $e->getMessage()])->withInput();
        }

    }


    public function logout() {

        session()->flush();  // Clear all session data
        return redirect()->route('/');

    }
}
