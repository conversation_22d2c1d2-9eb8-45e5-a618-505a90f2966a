<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        'examples/bayarcash/*',
        'v3/sso/login',
        'v3/sso/mobile-login',
        'v3/sso/csrf-refresh',
        'payment/bayarcash/*'
    ];
}
