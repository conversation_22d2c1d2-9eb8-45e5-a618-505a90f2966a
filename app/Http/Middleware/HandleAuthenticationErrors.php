<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class HandleAuthenticationErrors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $response = $next($request);
            
            // Check if this is a redirect to login page without explicit error
            if ($response->isRedirect() && $response->getTargetUrl() === url('/')) {
                // Log the silent redirect for debugging
                Log::warning('Silent redirect to login detected', [
                    'url' => $request->fullUrl(),
                    'method' => $request->method(),
                    'session_id' => $request->session()->getId(),
                    'has_auth_token' => Session::has('authToken'),
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]);
                
                // Add error message to session for user feedback
                if (!Session::has('errors') && !Session::has('error')) {
                    Session::flash('error', 'Sesi anda telah tamat. Sila log masuk semula.');
                }
            }
            
            return $response;
            
        } catch (\Exception $e) {
            Log::error('Authentication middleware error', [
                'error' => $e->getMessage(),
                'url' => $request->fullUrl(),
                'session_id' => $request->session()->getId(),
                'ip' => $request->ip()
            ]);
            
            // Redirect to login with error message
            return redirect('/')->withErrors(['error' => 'Ralat sistem berlaku. Sila cuba lagi.']);
        }
    }
}
