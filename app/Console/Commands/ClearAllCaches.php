<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class ClearAllCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all Laravel caches (config, route, view, application)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Clearing all caches...');
        
        // Clear application cache
        Artisan::call('cache:clear');
        $this->info('✓ Application cache cleared');
        
        // Clear configuration cache
        Artisan::call('config:clear');
        $this->info('✓ Configuration cache cleared');
        
        // Clear route cache
        Artisan::call('route:clear');
        $this->info('✓ Route cache cleared');
        
        // Clear view cache
        Artisan::call('view:clear');
        $this->info('✓ View cache cleared');
        
        // Clear compiled services
        Artisan::call('clear-compiled');
        $this->info('✓ Compiled services cleared');
        
        // Optimize for production if in production environment
        if (app()->environment('production')) {
            Artisan::call('config:cache');
            $this->info('✓ Configuration cached for production');
            
            Artisan::call('route:cache');
            $this->info('✓ Routes cached for production');
            
            Artisan::call('view:cache');
            $this->info('✓ Views cached for production');
        }
        
        $this->info('All caches cleared successfully!');
        
        return 0;
    }
}
