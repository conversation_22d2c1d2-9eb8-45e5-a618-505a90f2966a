<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Log;
use Exception;
use Livewire\Component;

class PaymentMethod extends Component
{

    public $chartData = [];
    public $errorMessage;

    public function mount() {
        $this->fetchChartRecord();
    }

    public function fetchChartRecord() {

        try {
            
            $curl = curl_init();

            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer '.session('authToken')
            ];

            curl_setopt_array($curl, array(
                CURLOPT_URL => env('API_URL').'/graph',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => $headers,
            ));

            $response = curl_exec($curl);
            curl_close($curl);

            // HTTP status code
            $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            // Check status code
            if ($http_status == 200) {

                $result = json_decode($response); // Assume response in JSON format

                if( $result->status == "1" && !empty($result->paymentMethod) ) { 

                    $this->chartData['chart'] = self::generateChart($result->paymentMethod);
                    $this->chartData['table'] = $result->paymentMethod;

                } else {

                    throw new Exception("Error Processing Request");                    
                }
            
            } else {

                throw new Exception("System is unable to retrieve the record. Please wait while retrying...");
            }
            
        } catch (Exception $e) {
            
            $this->errorMessage = $e->getMessage();
        }

    }

    private static function generateChart($data) {

        $arr=[
            'labels' => [],
            'records' => [],
            'background' => [],
            'amount' => [],
        ];


        foreach($data as $method) {

            $arr['labels'][] = $method->name;
            $arr['records'][] = $method->quantity;
            $arr['background'][] = $method->background;
            $arr['amount'][] = $method->amount;
        }

        return $arr;

    }

    public function render()
    {

        // Log::info('Chart data '.json_encode($this->chartData['chart']));

        return view('livewire.payment-method');
        // return view('livewire.payment-method', ['chartData'=>$this->chartData['chart']]);
    }
}
