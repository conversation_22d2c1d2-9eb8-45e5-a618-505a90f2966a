<?php

namespace App\Livewire;

use Exception;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\Attributes\Url;
use Illuminate\Support\Facades\Log;
use Livewire\WithPagination;

class CompanySales extends Component
{
    use WithPagination;

    // URL tracked filters
    #[Url(history: true)]
    public $search = '';

    #[Url(history: true)]
    public $userType = '';

    #[Url(history: true)]
    public $minSales = '';

    #[Url(history: true)]
    public $maxSales = '';

    #[Url(history: true)]
    public $comPbt = '';

    #[Url(history: true)]
    public $return_page;

    public $sortBy = 'total_sales';
    public $sortDir = 'DESC';
    public $pbts = [];
    public $tab = '2025';

    // Temporary filter values
    public $filters = [
        'searchQuery' => '',
        'userTypeQuery' => '',
        'salesRange' => [
            'min' => '',
            'max' => ''
        ],
        'com_pbt' => ''
    ];

    // public $companies = [];
    public $errorMessage = '';
    public $isLoading = false;
    protected $listeners = ['tab-changed' => 'handleTabChange'];

    // User type options - you can modify these based on your needs
    public $userTypeOptions = [
        '' => 'All Types',
        'retail' => 'Retail',
        'wholesale' => 'Wholesale',
        'distributor' => 'Distributor'
    ];

    #[Url]
    public int $currentPage = 1;

    public function mount($tab = null)
    {
        // Initialize filter values from URL parameters
        $this->filters['searchQuery'] = $this->search;
        $this->filters['userTypeQuery'] = $this->userType;
        $this->filters['salesRange']['min'] = $this->minSales;
        $this->filters['salesRange']['max'] = $this->maxSales;
        $this->filters['com_pbt'] = $this->comPbt;

        $this->currentPage = request()->query('page', 1);
        $this->tab = 'today';
        $this->fetchRecord($this->tab);
    }

    public function applyFilters()
    {
        $this->userType = $this->filters['userTypeQuery'];
        $this->minSales = $this->filters['salesRange']['min'];
        $this->maxSales = $this->filters['salesRange']['max'];
        $this->comPbt = $this->filters['com_pbt'];

        $this->currentPage = 1; // Reset to first page when filtering
        $this->fetchRecord($this->tab);
    }

    public function resetFilters()
    {
        $this->reset('search', 'userType', 'minSales', 'maxSales', 'comPbt');
        $this->filters = [
            'searchQuery' => '',
            'userTypeQuery' => '',
            'salesRange' => [
                'min' => '',
                'max' => ''
            ],
            'com_pbt' => ''
        ];
        $this->tab = $this->tab ?? date('Y');
        $this->currentPage = 1;
        $this->fetchRecord($this->tab);
    }

    public function sortTable($sortBy)
    {
        $this->tab = $this->tab ?? date('Y');
        $this->sortBy = $sortBy;
        $this->sortDir = $this->sortDir === 'ASC' ? 'DESC' : 'ASC';
        $this->fetchRecord($this->tab);
    }

    public function fetchRecord($tab)
    {
        $start_date = Carbon::now()->startOfDay()->format('Y-m-d');
        $end_date = Carbon::now()->endOfDay()->format('Y-m-d');

        if ($tab && $tab == 'all') {
            $start_date = Carbon::createFromDate(2024, 1, 1)->startOfYear()->format('Y-m-d');
            $end_date = Carbon::now()->endOfDay()->format('Y-m-d');
        } else if ($tab && $tab == date('Y') - 1) { // last year
            $start_date = Carbon::createFromDate(date('Y') - 1, 1, 1)->startOfYear()->format('Y-m-d');
            $end_date = Carbon::createFromDate(date('Y') - 1, 1, 1)->endOfYear()->format('Y-m-d');
        } else if ($tab && $tab == date('Y')) {  // this year
            $start_date = Carbon::createFromDate(date('Y'), 1, 1)->startOfYear()->format('Y-m-d');
            $end_date = Carbon::createFromDate(date('Y'), 1, 1)->endOfYear()->format('Y-m-d');
        } else if ($tab && $tab == 'today') {
            $start_date = Carbon::now()->startOfDay()->format('Y-m-d');
            $end_date = Carbon::now()->endOfDay()->format('Y-m-d');
        }

        try {
            $this->isLoading = true;
            $this->errorMessage = '';

            $curl = curl_init();

            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . session('authToken')
            ];

            $queryParams = http_build_query([
                'tab' => $tab,
                'start_date' => $start_date,
                'end_date' => $end_date,
                'search' => $this->search,
                'userType' => $this->userType,
                'minSales' => $this->minSales,
                'maxSales' => $this->maxSales,
                'comPbt' => $this->comPbt,
                'sortBy' => $this->sortBy,
                'sortDir' => $this->sortDir,
                'page' => request()->get('page', 1)
            ]);

            curl_setopt_array($curl, array(
                CURLOPT_URL => env('API_URL') . '/company-sales?' . $queryParams,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => $headers,
            ));

            $response = curl_exec($curl);
            // HTTP status code
            $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            // // Debug: Let's see what we're getting
            // dd([
            //     'http_status' => $http_status,
            //     'response' => $response,
            //     'decoded' => json_decode($response),
            //     'tab' => $tab,
            //     'query_params' => $queryParams
            // ]);

            // Check status code
            if ($http_status == 200) {

                $result = json_decode($response); // Assume response in JSON format

                if ($result->status == "1") {

                    // Store PBT data for dropdowns/filters
                    $this->pbts = $result->pbt_selangor ?? [];

                    // Create a proper paginated collection for the view using 'list' instead of 'companies'
                    $companiesData = collect($result->list->data ?? []);
                    $pagination = new \Illuminate\Pagination\LengthAwarePaginator(
                        $companiesData,
                        $result->list->total ?? 0,
                        $result->list->per_page ?? 10,
                        $result->list->current_page ?? 1,
                        [
                            'path' => request()->url(),
                            'pageName' => 'page'
                        ]
                    );

                    return $pagination;

                } else {
                    throw new Exception("Error Processing Request");
                }
            } else {
                Log::info('Unable to reach backend server ' . $http_status . ' - Using fallback data');

                // Fallback data when API is not available
                $mockCompanies = collect([
                    (object)[
                        'user_id' => 1,
                        'com_name' => 'Test Company 1',
                        'pbt_code' => 'PBT001',
                        'com_address' => 'Test Address 1',
                        'pbt_name' => 'Test PBT 1',
                        'category' => 'Retail',
                        'first_name' => 'Test User 1',
                        'latitude' => '3.1390',
                        'longitude' => '101.6869',
                        'total_sales' => 0
                    ]
                ]);

                return new \Illuminate\Pagination\LengthAwarePaginator(
                    $mockCompanies,
                    $mockCompanies->count(),
                    10,
                    1,
                    [
                        'path' => request()->url(),
                        'pageName' => 'page'
                    ]
                );
            }

        } catch (\Exception $e) {
            Log::error('API Error: ' . $e->getMessage());
            $this->errorMessage = 'Failed to load data: ' . $e->getMessage();

            // Return an empty paginated collection in case of error
            return new \Illuminate\Pagination\LengthAwarePaginator(
                collect([]),
                0,
                10,
                1,
                [
                    'path' => request()->url(),
                    'pageName' => 'page'
                ]
            );
        } finally {
            $this->isLoading = false;
        }
    }

    public function validateSalesRange()
    {
        // Convert to numeric for comparison
        $min = is_numeric($this->filters['salesRange']['min']) ? floatval($this->filters['salesRange']['min']) : null;
        $max = is_numeric($this->filters['salesRange']['max']) ? floatval($this->filters['salesRange']['max']) : null;

        if ($min !== null && $max !== null && $min > $max) {
            $this->errorMessage = 'Jualan minima tidak boleh melebihi jualan maksima';
            return false;
        }

        return true;
    }

    public function handleTabChange($tab)
    {
        $this->tab = $tab;
        $this->fetchRecord($this->tab);
    }

    public function render()
    {
        $companies = $this->fetchRecord($this->tab);
        return view('livewire.company-sales', [
            'companies' => $companies,
        ]);
    }
}
