<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        @hasSection('title')
            <title>@yield('title') - {{ config('app.name') }}</title>
        @else
            <title>{{ config('app.name') }}</title>
        @endif

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ url(asset('images/logo/pos_logo_bak.png')) }}">

    <!-- Fonts -->
    {{-- <link rel="stylesheet" href="https://rsms.me/inter/inter.css"> --}}
        <!-- Favicon -->
		<link rel="shortcut icon" href="{{ url(asset('images/logo/pos_logo_bak.png')) }}">
        
        <!-- P<PERSON> Manifest -->
        <link rel="manifest" href="{{ asset('manifest.json') }}">
        <meta name="theme-color" content="#2563eb">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="BizAppos MBI">
        <link rel="apple-touch-icon" href="{{ asset('images/logo/mbi.png') }}">

        <!-- PWA Meta Tags -->
        <meta name="theme-color" content="#2563eb">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="MBI Dashboard">
        <meta name="mobile-web-app-capable" content="yes">
        <meta name="application-name" content="MBI Dashboard">
        
        <!-- PWA Manifest -->
        <link rel="manifest" href="{{ asset('manifest.json') }}">
        
        <!-- Apple Touch Icons -->
        <link rel="apple-touch-icon" href="{{ url(asset('images/logo/pos_logo_bak.png')) }}">
        <link rel="apple-touch-icon" sizes="152x152" href="{{ url(asset('images/logo/pos_logo_bak.png')) }}">
        <link rel="apple-touch-icon" sizes="180x180" href="{{ url(asset('images/logo/pos_logo_bak.png')) }}">
        <link rel="apple-touch-icon" sizes="167x167" href="{{ url(asset('images/logo/pos_logo_bak.png')) }}">

        <!-- Fonts -->
        {{-- <link rel="stylesheet" href="https://rsms.me/inter/inter.css"> --}}

    @vite(['resources/sass/app.scss', 'resources/js/app.js'])
    @livewireStyles

    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    @yield('head-script')

    </head>

    <body class="@auth authenticated @endauth">
        @yield('body')
        
        @yield('foot-script')

        <!-- PWA Installation Script -->
        <script src="{{ asset('js/pwa-install.js') }}"></script>

        @stack('scripts')

        @livewireScripts

        <!-- PWA Service Worker Registration -->
        <script>
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                    navigator.serviceWorker.register('/sw.js')
                        .then(function(registration) {
                            console.log('ServiceWorker registration successful with scope: ', registration.scope);
                        })
                        .catch(function(error) {
                            console.log('ServiceWorker registration failed: ', error);
                        });
                });
            }
        </script>

        <!-- PWA Install Prompt -->
        <script src="{{ asset('js/pwa-install.js') }}"></script>
        
        <!-- PWA Status Indicator -->
        <script src="{{ asset('js/pwa-status.js') }}"></script>

    </body>


    <script>
        // start: Sidebar
        const sidebarToggle = document.querySelector('.sidebar-toggle')
        const sidebarOverlay = document.querySelector('.sidebar-overlay')
        const sidebarMenu = document.querySelector('.sidebar-menu')
        const main = document.querySelector('.main')
         // Ensure sidebar is hidden by default on small devices
        if (window.innerWidth < 1024) {
            sidebarMenu.classList.add('-translate-x-full');
            sidebarOverlay.classList.add('hidden');
        }

        sidebarToggle.addEventListener('click', function (e) {
            e.preventDefault()
            main.classList.toggle('active')
            sidebarOverlay.classList.toggle('hidden')
            sidebarMenu.classList.toggle('-translate-x-full')
        })
        sidebarOverlay.addEventListener('click', function (e) {
            e.preventDefault()
            main.classList.add('active')
            sidebarOverlay.classList.add('hidden')
            sidebarMenu.classList.add('-translate-x-full')
        })
    
    
        // Function to toggle the dropdown menu
      function toggleMenu() {
          const menu = document.getElementById('dropdownMenu');
          menu.classList.toggle('hidden');
      }
    
      // Optional: Close menu if clicked outside
      window.onclick = function(event) {
          if (!event.target.matches('#profileImage')) {
              const menu = document.getElementById('dropdownMenu');
              if (!menu.classList.contains('hidden')) {
                  menu.classList.add('hidden');
              }
          }
      }
      
    </script>
</html>
