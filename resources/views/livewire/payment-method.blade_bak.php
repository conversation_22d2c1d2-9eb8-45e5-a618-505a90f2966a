{{-- <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-6 my-4" wire:poll.60000ms="fetchChartRecord"> --}}
<div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-4 my-4">
    <x-card class="px-6 py-4 rounded-xl h-full">
        <div id="horizontalBarChart" class="h-94"></div>
    </x-card>

    {{-- <x-card class="px-6 py-4 rounded-xl h-full"> --}}

        <table class="w-full bg-white overflow-hidden border border-gray-700 rounded-xl sm:shadow-lg">
            <thead class="text-white uppercase text-xs">
                <tr class="bg-pos-light-200 mb-2 sm:mb-0 w-full">
                    <th class="p-3 text-left md:w-[50%] border border-gray-200">Saluran Pembayaran</th>
                    <th class="p-3 text-center md:w-[25%] border border-gray-200">Jum<PERSON> Transaks<PERSON></th>
                    <th class="p-3 text-right md:w-[25%] border border-gray-200">Jumlah Bayaran</th>
                </tr>
            </thead>
            @forelse($chartData['table'] as $key => $data)
                <tr class="text-xs text-gray-500 hover:bg-gray-100">
                    <td class="p-2 border border-gray-200">{{ $data->name }}
                        {{-- <div class="flex items-center">
                            <div class="w-3 h-3 mr-2" style="background-color: {{$data->background}} "></div> <!-- Kotak Kecik -->
                            <span>{{ $data->name }}</span>
                        </div> --}}
                    </td>
                    <td class="p-2 text-center border border-gray-200">{{ $data->quantity }}</td>
                    <td class="p-2 text-right border border-gray-200">{{ moneyFormat($data->amount) }}</td>
                </tr>
            @empty
            <tr>
                <td colspan="3">No record(s)</td>
            </tr>
            @endforelse
        </table>

    {{-- </x-card> --}}
</div>

@push('scripts')
<script>
    
    const options = {
        chart: {
            type: 'area',
            height: 350
        },
        series: [{
            name: 'Transaksi',
            data: @json($chartData['chart']['records'])
        }, {
            name: 'RM',
            data: @json($chartData['chart']['amount'])
        }],
        xaxis: {
            categories: @json($chartData['chart']['labels'])
        },
        stroke: {
            curve: 'smooth' // Makes it a spline (smooth) area chart
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.7,
                stops: [0, 100, 100]
            }
        },
        colors: ['#9C94EA','#4D3F7B'], // Warna untuk setiap series
        dataLabels: {
            enabled: false
        },
        tooltip: {
            x: {
                format: 'dd/MM/yy HH:mm'
            }
        }
    };

    const chart = new ApexCharts(document.querySelector("#horizontalBarChart"), options);
    chart.render();
       

</script>
@endpush