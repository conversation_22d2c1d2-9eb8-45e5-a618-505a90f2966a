{{-- <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-6 my-4" wire:poll.60000ms="fetchChartRecord"> --}}
<div
    class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-4 my-4 motion-translate-y-in-100 motion-duration-[1.2s] motion-ease-spring-smooth">
    <div>
        <x-card class="px-6 py-4 rounded-xl h-full">
            <div wire:ignore id="horizontalBarChart"></div>
        </x-card>
    </div>
    <div>
        <table class="h-full w-full bg-white overflow-hidden border border-gray-700 rounded-xl sm:shadow-lg">
            <thead class="text-white uppercase text-xs">
                <tr class="bg-pos-light-200 mb-2 sm:mb-0 w-full">
                    <th class="p-3 text-left md:w-[50%] border border-gray-200">Saluran Pembayaran</th>
                    <th class="p-3 text-center md:w-[25%] border border-gray-200">Jumlah Transaksi</th>
                    <th class="p-3 text-right md:w-[25%] border border-gray-200">Jumlah Bayaran</th>
                </tr>
            </thead>
            <tbody>
                @if (count($paymentMethod) == 0)
                    <tr>
                        <td colspan="3" class="text-center p-4">Tiada rekod</td>
                    </tr>
                @else
                    @foreach ($paymentMethod as $key => $pm)

                    <tr class="text-xs text-gray-500 hover:bg-gray-100">
                        <td class="p-2 border border-gray-200">{{ $pm->name ?? $key }}</td>
                        <td class="p-2 text-center border border-gray-200">{{ $pm->quantity ?? 0 }}</td>
                        <td class="p-2 text-right border border-gray-200">{{ moneyFormat($pm->amount ?? 0) }}</td>
                        </tr>
                    @endforeach
                @endif
            </tbody>
        </table>
    </div>
</div>



    @push('scripts')
        <script>
            // Global chart variable
            let chart = null;

            // Function to initialize or update chart
            function initChart(series, labels, colors) {
                console.log('Initializing chart with:', { series, labels, colors });

                const chartContainer = document.querySelector("#horizontalBarChart");
                if (!chartContainer) {
                    console.error('Chart container not found!');
                    return;
                }

                if (chart) {
                    chart.destroy();
                }

                const options = {
                    series: series,
                    labels: labels,
                    chart: {
                        height: 260,
                        type: 'pie',
                    },
                    colors: colors,  // Use dynamic colors
                    legend: {
                        show: true,
                        position: 'bottom',
                        horizontalAlign: 'center',
                    }
                };

                chart = new ApexCharts(chartContainer, options);
                chart.render();
            }

            // Listen for the Livewire event
            document.addEventListener('updateChart', event => {
                const { series, labels, colors } = event.detail[0];

                // if (series.length !== labels.length || labels.length !== colors.length) {
                //     console.error('Mismatch between labels, series, and colors:', { labels, series, colors });
                //     return;
                // }

                initChart(series, labels, colors);
            });

            // Initialize chart on page load
            document.addEventListener('DOMContentLoaded', function() {
                const initialSeries = @json($chartData['chart']['series'] ?? []);
                const initialLabels = @json($chartData['chart']['labels'] ?? []);
                const initialColors = @json($chartData['chart']['colors'] ?? []);
                initChart(initialSeries, initialLabels, initialColors);
            });
        </script>
    @endpush
