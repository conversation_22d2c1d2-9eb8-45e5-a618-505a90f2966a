<div>
    <div class="pl-4">
        <div class="grid grid-cols-2 mb-5 motion-translate-y-in-100 motion-duration-[1.2s] motion-ease-spring-smooth">
            <h2 class="text-2xl font-semibold mb-[-5px] pb-0">{{ $profile->com_name }}</h2>

    
            <div class="flex justify-end pr-10">
                <button onclick="window.location.href='/dashboard'"
                    class="bg-blue-600 text-white px-4 py-2 rounded-md shadow-md flex items-center text-sm hover:bg-blue-700">
                    <x-heroicon-o-arrow-long-left class="mr-2 h-5 w-5" /> Kembali
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-10 mb-5">
            <div class="lg:col-span-3">
                <div class="bg-white p-6 rounded-xl shadow-sm mb-4">
                    <x-merchant.business-info companyName="{{ $profile->com_name }}"
                        companyAddress="{{ $profile->com_address }}" companyMobile="{{ $profile->com_mobile }}"
                        companyEmail="{{ $profile->com_email }}" companyStatus="{{ $profile->status }}"
                        companyPbt="{{ strtoupper($pbt?->name ?? 'Tiada Data') }}"
                        companyRegisterDate="{{ date('d F Y', strtotime($profile->created_at)) }}" />
                </div>

                <div class="bg-white p-6 rounded-xl shadow-sm mb-4">
                    @if ($topProducts && count($topProducts) > 0)
                        <x-merchant.top-products :topProducts="$topProducts" />
                    @else
                        <div class="text-center py-8">
                            <p class="text-gray-500 text-sm">Tiada data produk terjual yang mencukupi</p>
                            <p class="text-gray-400 text-xs mt-1">Taburan data produk akan dipapar disini.</p>
                        </div>
                    @endif
                </div>

                <div class="bg-white p-6 rounded-xl shadow-sm mb-4">
                    <x-merchant.payment-channel :paymentChannels="$paymentMethods" />
                </div>
            </div>

            <div class="lg:col-span-7 lg:ml-4">
                <x-merchant.total-sales totalSales="{{ moneyFormat($totalSales, 2, '') }}"
                    lastActive="{{ $lastActivity && $lastActivity->updated_at
                        ? \Carbon\Carbon::parse($lastActivity->updated_at)->format('d/m/y h:i:s A')
                        : 'N/A' }}" />


                {{-- <div class="bg-white p-6 rounded-xl shadow-sm mb-4">
                    <x-merchant.sales-chart :labels="$labels" :data="$data" />
                </div> --}}
                {{-- @dd($dailySales) --}}
                <div id="horizontalBarChart"></div>

            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        var options = {
            chart: {
                type: 'area',
                height: 350
            },
            series: [{
                name: 'RM',
                data: @json($data) // Use the $data array for sales values
            }],
            xaxis: {
                categories: @json($labels) // Use the $labels array for dates
            },
            stroke: {
                curve: 'smooth' // Makes it a spline (smooth) area chart
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.7,
                    stops: [0, 100, 100]
                }
            },
            colors: ['#9C94EA', '#4D3F7B'], 
            dataLabels: {
                enabled: false
            },
            tooltip: {
                x: {
                    format: 'dd/MM/yy HH:mm'
                }
            }
        };

        var chart = new ApexCharts(document.querySelector("#horizontalBarChart"), options);
        chart.render();
    </script>
@endpush
