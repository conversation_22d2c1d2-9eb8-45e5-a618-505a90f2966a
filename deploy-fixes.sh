#!/bin/bash

# Bizappos MBI CSRF and Authentication Fix Deployment Script
# This script applies all the fixes for CSRF token and authentication issues

echo "🚀 Deploying CSRF and Authentication Fixes for Bizappos MBI..."

# Check if we're in the correct directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: artisan file not found. Please run this script from the Laravel project root."
    exit 1
fi

echo "📋 Step 1: Clearing all caches..."
php artisan cache:clear-all

echo "📋 Step 2: Checking file permissions for session storage..."
chmod -R 775 storage/framework/sessions
chmod -R 775 storage/logs

echo "📋 Step 3: Verifying middleware files exist..."
if [ ! -f "app/Http/Middleware/VerifyCsrfToken.php" ]; then
    echo "❌ Error: VerifyCsrfToken middleware not found!"
    exit 1
fi

if [ ! -f "app/Http/Middleware/EnsureSessionStarted.php" ]; then
    echo "❌ Error: EnsureSessionStarted middleware not found!"
    exit 1
fi

if [ ! -f "app/Http/Middleware/HandleAuthenticationErrors.php" ]; then
    echo "❌ Error: HandleAuthenticationErrors middleware not found!"
    exit 1
fi

echo "✅ All middleware files verified"

echo "📋 Step 4: Running configuration optimizations..."
if [ "$APP_ENV" = "production" ]; then
    echo "🏭 Production environment detected - optimizing..."
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
else
    echo "🔧 Development environment - skipping optimizations"
fi

echo "📋 Step 5: Testing CSRF token generation..."
php artisan tinker --execute="
echo 'Session driver: ' . config('session.driver') . PHP_EOL;
echo 'Session lifetime: ' . config('session.lifetime') . ' minutes' . PHP_EOL;
echo 'CSRF token generated: ' . (csrf_token() ? 'Yes' : 'No') . PHP_EOL;
"

echo "📋 Step 6: Checking log file permissions..."
touch storage/logs/laravel-$(date +%Y-%m-%d).log
chmod 664 storage/logs/laravel-$(date +%Y-%m-%d).log

echo "✅ Deployment completed successfully!"
echo ""
echo "🔍 Next steps:"
echo "1. Test login functionality in a private/incognito browser window"
echo "2. Monitor logs at storage/logs/laravel-$(date +%Y-%m-%d).log"
echo "3. Check /debug-csrf endpoint to verify CSRF token generation"
echo "4. Remove debug routes in production environment"
echo ""
echo "🐛 If issues persist:"
echo "- Check browser console for JavaScript errors"
echo "- Verify session storage permissions: ls -la storage/framework/sessions"
echo "- Monitor authentication logs for silent redirect patterns"
echo ""
echo "📊 Log monitoring command:"
echo "tail -f storage/logs/laravel-$(date +%Y-%m-%d).log | grep -E '(Login|CSRF|419|ERROR)'"
