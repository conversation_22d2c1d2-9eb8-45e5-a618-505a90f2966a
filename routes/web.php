<?php

use App\Http\Controllers\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MerchantController;
use App\Http\Controllers\StatisticsController;
use App\Livewire\Merchant;
use Livewire\Livewire;

// use App\Http\Livewire\Merchant;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('login');
})->middleware('ensure.session')->name('/');

Route::post('login', [Auth::class, 'login'])->middleware('ensure.session')->name('authenticate');
Route::get('/login', function () {
    return redirect('/');
});

// Debug route to check CSRF token (remove in production)
Route::get('/debug-csrf', function () {
    return response()->json([
        'session_id' => session()->getId(),
        'csrf_token' => csrf_token(),
        'session_token' => session()->token(),
        'session_started' => session()->isStarted(),
    ]);
});

Route::group(['middleware'=>'auth.mbi'], function() {

    Route::view('/dashboard', 'dashboard')->name('dashboard');
    Route::get('/merchant', [MerchantController::class, 'index'])->name('merchant');
    Route::get('/statistics', [StatisticsController::class, 'index'])->name('statistics');
    Route::get('/merchant/show/{user_id}', [MerchantController::class,'show'])->name('merchant.show');

    Route::get('/test', function () {
        return Livewire::test(\App\Livewire\Merchant::class, ['userId' => 123]);
    });
    
    // PWA test route
    Route::get('/pwa-test', function () {
        return view('pwa-test');
    })->name('pwa.test');
    
    Route::get('logout', [Auth::class, 'logout'])->name('logout');
});

// sample route check token
// Route::get('/secure-route', function () {
//     return "You have a valid token!";
// })->middleware('check.token');